<?xml version="1.0" ?>
<coverage version="7.8.0" timestamp="1748382781823" lines-valid="2966" lines-covered="2733" line-rate="0.9214" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.8.0 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Documents/BRAIN/pb-agro-report-rgca-api</source>
	</sources>
	<packages>
		<package name="pb_agro_report_rgca_api" line-rate="0.9429" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
					</lines>
				</class>
				<class name="api.py" filename="pb_agro_report_rgca_api/api.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
					</lines>
				</class>
				<class name="api_auth.py" filename="pb_agro_report_rgca_api/api_auth.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
					</lines>
				</class>
				<class name="api_config.py" filename="pb_agro_report_rgca_api/api_config.py" complexity="0" line-rate="0.9" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="0"/>
						<line number="19" hits="1"/>
					</lines>
				</class>
				<class name="celery.py" filename="pb_agro_report_rgca_api/celery.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="10" hits="1"/>
					</lines>
				</class>
				<class name="celery_config.py" filename="pb_agro_report_rgca_api/celery_config.py" complexity="0" line-rate="0.8235" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="0"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="0"/>
						<line number="21" hits="0"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1"/>
						<line number="47" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.enums" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/enums/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="dataset.py" filename="pb_agro_report_rgca_api/enums/dataset.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
					</lines>
				</class>
				<class name="report_status.py" filename="pb_agro_report_rgca_api/enums/report_status.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
					</lines>
				</class>
				<class name="sub_modules.py" filename="pb_agro_report_rgca_api/enums/sub_modules.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.models" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/models/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
					</lines>
				</class>
				<class name="report_rgca.py" filename="pb_agro_report_rgca_api/models/report_rgca.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.routers" line-rate="0.4308" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/routers/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
					</lines>
				</class>
				<class name="health.py" filename="pb_agro_report_rgca_api/routers/health.py" complexity="0" line-rate="0.8333" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="0"/>
					</lines>
				</class>
				<class name="report_rgca.py" filename="pb_agro_report_rgca_api/routers/report_rgca.py" complexity="0" line-rate="0.3684" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="51" hits="0"/>
						<line number="53" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="81" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="94" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="106" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.schemas" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/schemas/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
					</lines>
				</class>
				<class name="health.py" filename="pb_agro_report_rgca_api/schemas/health.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
					</lines>
				</class>
				<class name="report_rgca.py" filename="pb_agro_report_rgca_api/schemas/report_rgca.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.services" line-rate="0.8891" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/services/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
					</lines>
				</class>
				<class name="agro_related_api.py" filename="pb_agro_report_rgca_api/services/agro_related_api.py" complexity="0" line-rate="0.9583" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="0"/>
						<line number="27" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
					</lines>
				</class>
				<class name="agro_report_api.py" filename="pb_agro_report_rgca_api/services/agro_report_api.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="43" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
					</lines>
				</class>
				<class name="agrowatch_service.py" filename="pb_agro_report_rgca_api/services/agrowatch_service.py" complexity="0" line-rate="0.7143" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="50" hits="1"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
					</lines>
				</class>
				<class name="basic_info_service.py" filename="pb_agro_report_rgca_api/services/basic_info_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="23" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
					</lines>
				</class>
				<class name="bndes_service.py" filename="pb_agro_report_rgca_api/services/bndes_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
					</lines>
				</class>
				<class name="cloud_ranger_service.py" filename="pb_agro_report_rgca_api/services/cloud_ranger_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
					</lines>
				</class>
				<class name="credit_service.py" filename="pb_agro_report_rgca_api/services/credit_service.py" complexity="0" line-rate="0.9608" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="71" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="84" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="98" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="114" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="131" hits="1"/>
						<line number="133" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="144" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="152" hits="1"/>
						<line number="156" hits="1"/>
						<line number="158" hits="1"/>
						<line number="162" hits="1"/>
						<line number="164" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="169" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="184" hits="1"/>
					</lines>
				</class>
				<class name="delivery_api_service.py" filename="pb_agro_report_rgca_api/services/delivery_api_service.py" complexity="0" line-rate="0.9459" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
					</lines>
				</class>
				<class name="diagnosis_service.py" filename="pb_agro_report_rgca_api/services/diagnosis_service.py" complexity="0" line-rate="0.7652" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="44" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="85" hits="1"/>
						<line number="87" hits="1"/>
						<line number="90" hits="1"/>
						<line number="94" hits="1"/>
						<line number="101" hits="0"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="112" hits="1"/>
						<line number="114" hits="1"/>
						<line number="118" hits="1"/>
						<line number="121" hits="1"/>
						<line number="123" hits="1"/>
						<line number="127" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="165" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="174" hits="1"/>
						<line number="176" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="192" hits="1"/>
						<line number="194" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="202" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="207" hits="1"/>
						<line number="214" hits="1"/>
						<line number="216" hits="1"/>
						<line number="220" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="0"/>
						<line number="224" hits="0"/>
						<line number="231" hits="0"/>
						<line number="238" hits="0"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
					</lines>
				</class>
				<class name="document_service.py" filename="pb_agro_report_rgca_api/services/document_service.py" complexity="0" line-rate="0.8537" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="15" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="55" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="0"/>
						<line number="60" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
					</lines>
				</class>
				<class name="esg_service.py" filename="pb_agro_report_rgca_api/services/esg_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="57" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
					</lines>
				</class>
				<class name="excel_service.py" filename="pb_agro_report_rgca_api/services/excel_service.py" complexity="0" line-rate="0.887" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="71" hits="1"/>
						<line number="74" hits="1"/>
						<line number="77" hits="1"/>
						<line number="81" hits="1"/>
						<line number="85" hits="1"/>
						<line number="88" hits="1"/>
						<line number="92" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="132" hits="1"/>
						<line number="142" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="159" hits="1"/>
						<line number="161" hits="1"/>
						<line number="171" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="183" hits="1"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="0"/>
						<line number="191" hits="1"/>
						<line number="202" hits="1"/>
						<line number="203" hits="0"/>
						<line number="207" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="217" hits="1"/>
						<line number="218" hits="1"/>
						<line number="225" hits="1"/>
						<line number="226" hits="1"/>
						<line number="227" hits="1"/>
						<line number="228" hits="1"/>
						<line number="230" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="249" hits="1"/>
						<line number="258" hits="1"/>
						<line number="267" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="270" hits="1"/>
						<line number="272" hits="1"/>
						<line number="277" hits="1"/>
						<line number="279" hits="1"/>
						<line number="286" hits="1"/>
						<line number="288" hits="1"/>
						<line number="295" hits="1"/>
						<line number="296" hits="1"/>
						<line number="298" hits="1"/>
						<line number="299" hits="1"/>
						<line number="300" hits="1"/>
						<line number="301" hits="1"/>
						<line number="303" hits="1"/>
						<line number="309" hits="1"/>
						<line number="314" hits="1"/>
						<line number="315" hits="1"/>
						<line number="316" hits="1"/>
						<line number="320" hits="1"/>
						<line number="321" hits="1"/>
						<line number="322" hits="1"/>
						<line number="323" hits="1"/>
						<line number="325" hits="1"/>
						<line number="335" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="0"/>
						<line number="350" hits="1"/>
						<line number="351" hits="0"/>
						<line number="363" hits="1"/>
						<line number="364" hits="0"/>
						<line number="376" hits="1"/>
						<line number="377" hits="0"/>
						<line number="389" hits="0"/>
						<line number="390" hits="0"/>
						<line number="392" hits="1"/>
						<line number="393" hits="0"/>
						<line number="395" hits="1"/>
						<line number="397" hits="1"/>
						<line number="399" hits="1"/>
						<line number="400" hits="1"/>
						<line number="402" hits="1"/>
						<line number="403" hits="1"/>
						<line number="405" hits="1"/>
						<line number="406" hits="1"/>
						<line number="407" hits="1"/>
						<line number="409" hits="1"/>
						<line number="411" hits="1"/>
						<line number="413" hits="1"/>
						<line number="414" hits="1"/>
						<line number="415" hits="1"/>
						<line number="417" hits="1"/>
						<line number="448" hits="1"/>
						<line number="449" hits="1"/>
						<line number="452" hits="1"/>
						<line number="453" hits="1"/>
						<line number="454" hits="1"/>
						<line number="455" hits="1"/>
						<line number="457" hits="1"/>
						<line number="458" hits="1"/>
						<line number="459" hits="1"/>
						<line number="464" hits="1"/>
						<line number="466" hits="1"/>
						<line number="467" hits="1"/>
						<line number="468" hits="1"/>
						<line number="470" hits="1"/>
						<line number="471" hits="0"/>
						<line number="472" hits="0"/>
						<line number="473" hits="0"/>
						<line number="474" hits="0"/>
						<line number="475" hits="0"/>
						<line number="476" hits="0"/>
						<line number="477" hits="0"/>
						<line number="479" hits="1"/>
						<line number="480" hits="1"/>
					</lines>
				</class>
				<class name="file_service.py" filename="pb_agro_report_rgca_api/services/file_service.py" complexity="0" line-rate="0.8788" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="0"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
					</lines>
				</class>
				<class name="get_iam_token.py" filename="pb_agro_report_rgca_api/services/get_iam_token.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1"/>
					</lines>
				</class>
				<class name="intersection_service.py" filename="pb_agro_report_rgca_api/services/intersection_service.py" complexity="0" line-rate="0.6383" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="58" hits="0"/>
						<line number="64" hits="0"/>
						<line number="68" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="111" hits="1"/>
						<line number="112" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
					</lines>
				</class>
				<class name="land_service.py" filename="pb_agro_report_rgca_api/services/land_service.py" complexity="0" line-rate="0.9362" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="0"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="81" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="129" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="138" hits="1"/>
						<line number="142" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="164" hits="1"/>
					</lines>
				</class>
				<class name="protests_service.py" filename="pb_agro_report_rgca_api/services/protests_service.py" complexity="0" line-rate="0.9167" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="0"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
					</lines>
				</class>
				<class name="rectification_car.py" filename="pb_agro_report_rgca_api/services/rectification_car.py" complexity="0" line-rate="0.9714" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="0"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="48" hits="1"/>
						<line number="50" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
					</lines>
				</class>
				<class name="sintegra_service.py" filename="pb_agro_report_rgca_api/services/sintegra_service.py" complexity="0" line-rate="0.9231" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="0"/>
						<line number="19" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="0"/>
						<line number="39" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
					</lines>
				</class>
				<class name="soil_use_service.py" filename="pb_agro_report_rgca_api/services/soil_use_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="33" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="85" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="90" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="100" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
					</lines>
				</class>
				<class name="teams_messages.py" filename="pb_agro_report_rgca_api/services/teams_messages.py" complexity="0" line-rate="0.8889" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
					</lines>
				</class>
				<class name="utils_service.py" filename="pb_agro_report_rgca_api/services/utils_service.py" complexity="0" line-rate="0.8132" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="0"/>
						<line number="20" hits="0"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="107" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="129" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="143" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="151" hits="0"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="160" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="171" hits="1"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1"/>
						<line number="186" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="218" hits="1"/>
						<line number="220" hits="1"/>
						<line number="221" hits="1"/>
						<line number="227" hits="1"/>
						<line number="229" hits="1"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
						<line number="239" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.tasks" line-rate="0.9024" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/tasks/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
					</lines>
				</class>
				<class name="base.py" filename="pb_agro_report_rgca_api/tasks/base.py" complexity="0" line-rate="0.8852" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="44" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="0"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="70" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="103" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="0"/>
					</lines>
				</class>
				<class name="diagnosis_report_task.py" filename="pb_agro_report_rgca_api/tasks/diagnosis_report_task.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
					</lines>
				</class>
				<class name="test.py" filename="pb_agro_report_rgca_api/tasks/test.py" complexity="0" line-rate="0.8571" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.tests" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/tests/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.tests.services" line-rate="0.9727" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/tests/services/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="test_agro_related_api.py" filename="pb_agro_report_rgca_api/tests/services/test_agro_related_api.py" complexity="0" line-rate="0.9804" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="0"/>
					</lines>
				</class>
				<class name="test_agro_report_api.py" filename="pb_agro_report_rgca_api/tests/services/test_agro_report_api.py" complexity="0" line-rate="0.9846" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="60" hits="1"/>
						<line number="64" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="112" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="0"/>
					</lines>
				</class>
				<class name="test_agrowatch_service.py" filename="pb_agro_report_rgca_api/tests/services/test_agrowatch_service.py" complexity="0" line-rate="0.6429" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="83" hits="1"/>
						<line number="84" hits="0"/>
					</lines>
				</class>
				<class name="test_api_auth.py" filename="pb_agro_report_rgca_api/tests/services/test_api_auth.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="50" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="75" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="82" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="1"/>
						<line number="98" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="109" hits="1"/>
					</lines>
				</class>
				<class name="test_basic_info_service.py" filename="pb_agro_report_rgca_api/tests/services/test_basic_info_service.py" complexity="0" line-rate="0.9773" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="0"/>
					</lines>
				</class>
				<class name="test_bndes_service.py" filename="pb_agro_report_rgca_api/tests/services/test_bndes_service.py" complexity="0" line-rate="0.9545" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="0"/>
					</lines>
				</class>
				<class name="test_cloud_ranger_service.py" filename="pb_agro_report_rgca_api/tests/services/test_cloud_ranger_service.py" complexity="0" line-rate="0.9545" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="0"/>
					</lines>
				</class>
				<class name="test_credit_service.py" filename="pb_agro_report_rgca_api/tests/services/test_credit_service.py" complexity="0" line-rate="0.9926" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="37" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="51" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="65" hits="1"/>
						<line number="70" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="124" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="132" hits="1"/>
						<line number="135" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="162" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="177" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="192" hits="1"/>
						<line number="193" hits="1"/>
						<line number="194" hits="1"/>
						<line number="195" hits="1"/>
						<line number="196" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="201" hits="1"/>
						<line number="202" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="207" hits="1"/>
						<line number="208" hits="1"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="215" hits="1"/>
						<line number="216" hits="1"/>
						<line number="218" hits="1"/>
						<line number="219" hits="1"/>
						<line number="220" hits="1"/>
						<line number="221" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="1"/>
						<line number="224" hits="1"/>
						<line number="226" hits="1"/>
						<line number="227" hits="1"/>
						<line number="229" hits="1"/>
						<line number="230" hits="1"/>
						<line number="232" hits="1"/>
						<line number="233" hits="1"/>
						<line number="240" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="245" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="0"/>
					</lines>
				</class>
				<class name="test_delivery_api_service.py" filename="pb_agro_report_rgca_api/tests/services/test_delivery_api_service.py" complexity="0" line-rate="0.9868" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="32" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="69" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="112" hits="1"/>
						<line number="123" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="153" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="164" hits="1"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="183" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="193" hits="1"/>
						<line number="194" hits="0"/>
					</lines>
				</class>
				<class name="test_diagnosis_service.py" filename="pb_agro_report_rgca_api/tests/services/test_diagnosis_service.py" complexity="0" line-rate="0.993" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="1"/>
						<line number="62" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="1"/>
						<line number="90" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="112" hits="1"/>
						<line number="115" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="160" hits="1"/>
						<line number="172" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="180" hits="1"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="193" hits="1"/>
						<line number="194" hits="1"/>
						<line number="195" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="207" hits="1"/>
						<line number="210" hits="1"/>
						<line number="224" hits="1"/>
						<line number="225" hits="1"/>
						<line number="228" hits="1"/>
						<line number="229" hits="1"/>
						<line number="233" hits="1"/>
						<line number="236" hits="1"/>
						<line number="239" hits="1"/>
						<line number="240" hits="1"/>
						<line number="244" hits="1"/>
						<line number="245" hits="1"/>
						<line number="246" hits="1"/>
						<line number="247" hits="1"/>
						<line number="248" hits="1"/>
						<line number="250" hits="1"/>
						<line number="251" hits="1"/>
						<line number="253" hits="1"/>
						<line number="254" hits="1"/>
						<line number="257" hits="1"/>
						<line number="264" hits="1"/>
						<line number="266" hits="1"/>
						<line number="269" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="276" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="1"/>
						<line number="282" hits="1"/>
						<line number="285" hits="1"/>
						<line number="288" hits="1"/>
						<line number="291" hits="1"/>
						<line number="292" hits="0"/>
					</lines>
				</class>
				<class name="test_document_service.py" filename="pb_agro_report_rgca_api/tests/services/test_document_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
					</lines>
				</class>
				<class name="test_esg_service.py" filename="pb_agro_report_rgca_api/tests/services/test_esg_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="45" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
					</lines>
				</class>
				<class name="test_excel_service.py" filename="pb_agro_report_rgca_api/tests/services/test_excel_service.py" complexity="0" line-rate="0.9945" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="84" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="177" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="193" hits="1"/>
						<line number="194" hits="1"/>
						<line number="195" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="215" hits="1"/>
						<line number="216" hits="1"/>
						<line number="218" hits="1"/>
						<line number="220" hits="1"/>
						<line number="221" hits="1"/>
						<line number="222" hits="1"/>
						<line number="225" hits="1"/>
						<line number="228" hits="1"/>
						<line number="229" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="237" hits="1"/>
						<line number="239" hits="1"/>
						<line number="240" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="245" hits="1"/>
						<line number="246" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1"/>
						<line number="250" hits="1"/>
						<line number="254" hits="1"/>
						<line number="255" hits="1"/>
						<line number="257" hits="1"/>
						<line number="258" hits="1"/>
						<line number="259" hits="1"/>
						<line number="263" hits="1"/>
						<line number="267" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="280" hits="1"/>
						<line number="281" hits="1"/>
						<line number="282" hits="1"/>
						<line number="286" hits="1"/>
						<line number="288" hits="1"/>
						<line number="289" hits="1"/>
						<line number="290" hits="1"/>
						<line number="292" hits="1"/>
						<line number="293" hits="1"/>
						<line number="294" hits="1"/>
						<line number="298" hits="1"/>
						<line number="299" hits="1"/>
						<line number="301" hits="1"/>
						<line number="302" hits="1"/>
						<line number="303" hits="1"/>
						<line number="305" hits="1"/>
						<line number="306" hits="1"/>
						<line number="307" hits="1"/>
						<line number="311" hits="1"/>
						<line number="323" hits="1"/>
						<line number="324" hits="1"/>
						<line number="325" hits="1"/>
						<line number="328" hits="1"/>
						<line number="329" hits="0"/>
					</lines>
				</class>
				<class name="test_file_service.py" filename="pb_agro_report_rgca_api/tests/services/test_file_service.py" complexity="0" line-rate="0.975" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="0"/>
					</lines>
				</class>
				<class name="test_get_iam_token.py" filename="pb_agro_report_rgca_api/tests/services/test_get_iam_token.py" complexity="0" line-rate="0.9783" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="0"/>
					</lines>
				</class>
				<class name="test_intersection_service.py" filename="pb_agro_report_rgca_api/tests/services/test_intersection_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="100" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="110" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
					</lines>
				</class>
				<class name="test_land_service.py" filename="pb_agro_report_rgca_api/tests/services/test_land_service.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="38" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="93" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="105" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="114" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="120" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="135" hits="1"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="183" hits="1"/>
						<line number="184" hits="1"/>
						<line number="187" hits="1"/>
						<line number="189" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="202" hits="1"/>
						<line number="204" hits="1"/>
						<line number="207" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="216" hits="1"/>
						<line number="219" hits="1"/>
						<line number="222" hits="1"/>
						<line number="224" hits="1"/>
						<line number="232" hits="1"/>
						<line number="235" hits="1"/>
						<line number="236" hits="1"/>
					</lines>
				</class>
				<class name="test_protests.py" filename="pb_agro_report_rgca_api/tests/services/test_protests.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
					</lines>
				</class>
				<class name="test_rectification_car.py" filename="pb_agro_report_rgca_api/tests/services/test_rectification_car.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
					</lines>
				</class>
				<class name="test_sintegra_service.py" filename="pb_agro_report_rgca_api/tests/services/test_sintegra_service.py" complexity="0" line-rate="0.9778" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="0"/>
					</lines>
				</class>
				<class name="test_soil_use_service.py" filename="pb_agro_report_rgca_api/tests/services/test_soil_use_service.py" complexity="0" line-rate="0.9818" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="72" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="111" hits="1"/>
						<line number="116" hits="1"/>
						<line number="121" hits="1"/>
						<line number="124" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="133" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="143" hits="1"/>
						<line number="146" hits="1"/>
						<line number="151" hits="1"/>
						<line number="155" hits="1"/>
						<line number="161" hits="1"/>
						<line number="164" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="0"/>
					</lines>
				</class>
				<class name="test_teams_messages.py" filename="pb_agro_report_rgca_api/tests/services/test_teams_messages.py" complexity="0" line-rate="0.95" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="0"/>
					</lines>
				</class>
				<class name="test_utils_service.py" filename="pb_agro_report_rgca_api/tests/services/test_utils_service.py" complexity="0" line-rate="0.989" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="110" hits="1"/>
						<line number="113" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="122" hits="1"/>
						<line number="126" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="137" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="150" hits="1"/>
						<line number="153" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="178" hits="1"/>
						<line number="180" hits="1"/>
						<line number="182" hits="1"/>
						<line number="183" hits="1"/>
						<line number="184" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="191" hits="1"/>
						<line number="192" hits="1"/>
						<line number="195" hits="1"/>
						<line number="196" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.tests.tasks" line-rate="0.9905" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="pb_agro_report_rgca_api/tests/tasks/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="test_base.py" filename="pb_agro_report_rgca_api/tests/tasks/test_base.py" complexity="0" line-rate="0.9863" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="67" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="81" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="105" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="130" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="0"/>
					</lines>
				</class>
				<class name="test_diagnosis_report_task.py" filename="pb_agro_report_rgca_api/tests/tasks/test_diagnosis_report_task.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="26" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="pb_agro_report_rgca_api.utils" line-rate="0.1786" branch-rate="0" complexity="0">
			<classes>
				<class name="build_failure.py" filename="pb_agro_report_rgca_api/utils/build_failure.py" complexity="0" line-rate="0.1786" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="0"/>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="12" hits="1"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="25" hits="1"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="34" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="49" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
