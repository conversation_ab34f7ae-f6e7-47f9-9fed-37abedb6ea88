import asyncio
import json
from datetime import datetime
from zoneinfo import ZoneInfo

import geopandas as gpd
import pandas as pd

from pb_agro_report_rgca_api.enums.sub_modules import SubModules
from pb_agro_report_rgca_api.services.agrowatch_service import AgrowatchService
from pb_agro_report_rgca_api.services.credit_service import CreditService
from pb_agro_report_rgca_api.services.esg_service import ESGService
from pb_agro_report_rgca_api.services.excel_service import ExcelService
from pb_agro_report_rgca_api.services.file_service import FileService
from pb_agro_report_rgca_api.services.land_service import LandService
from pb_agro_report_rgca_api.services.soil_use_service import SoilUseService
from pb_agro_report_rgca_api.services.utils_service import UtilsService


class DiagnosisService:
    def __init__(self):
        self.utils_service = UtilsService()
        self.agrowatch_service = AgrowatchService()
        self.file_service = FileService()
        self.excel_service = ExcelService()

    def generate_report(self, path_file, sub_modules, client_document):
        columns_to_remove = [
            "id",
            "document",
            "requester",
            "reference_date",
            "updated_at",
            "errorType",
            "errorType",
            "trace",
            "errorMessage",
        ]
        input_df = self.utils_service.read_input_csv(input_csv=path_file)
        if "documento" in input_df.columns:
            input_df["documento"] = input_df["documento"].apply(
                lambda x: self.utils_service.clean_document(x)
            )

        if (
            "documento" in input_df.columns
            and "car" not in input_df.columns
            and sub_modules != [SubModules.CREDIT.value]
        ):
            input_df = self.search_car_in_agrowatch_api(input_df)

        if "geom" not in input_df.columns and sub_modules != [SubModules.CREDIT.value]:
            input_df = self.get_car_geometries(input_df)

        sub_module_dict = {
            SubModules.CREDIT.value: {
                "col": "documento",
                "method": self._apply_credit,
                "kwargs": {"client_document": client_document},
            },
            SubModules.AGRICULTURAL.value: {
                "col": "car",
                "method": self._apply_soil_use,
            },
            SubModules.LAND.value: {
                "col": "car",
                "method": self._apply_environmental,
            },
            SubModules.ESG.value: {
                "col": "car",
                "method": self._apply_esg,
            },
        }

        for sub_module in sub_modules:
            col = sub_module_dict[sub_module]["col"]
            method = sub_module_dict[sub_module]["method"]
            params = sub_module_dict[sub_module].get("kwargs", {})
            if col in input_df.columns:
                input_df = method(input_df, **params)

        if (
            SubModules.CREDIT.value not in sub_modules
            and SubModules.ESG.value not in sub_modules
        ):
            columns_to_remove.append("documento")
        else:
            input_df["Documento"] = input_df["documento"].apply(
                lambda x: UtilsService.mask_document(x)
            )
            input_df["Tipo"] = input_df["Documento"].apply(
                lambda x: UtilsService.determine_document_type(x)
            )

        if "car" in input_df.columns and not any(
            [
                SubModules.AGRICULTURAL.value in sub_modules,
                SubModules.LAND.value in sub_modules,
                SubModules.ESG.value in sub_modules,
            ]
        ):
            columns_to_remove.append("car")
        else:
            input_df = self.utils_service.rename_column(input_df, "car", "CAR")

        today_date = datetime.now(ZoneInfo("America/Sao_Paulo")).strftime("%d/%m/%Y")
        input_df["Data da Consulta"] = today_date
        responses_df = self.utils_service.remove_columns(
            input_df,
            columns_to_remove,
        )

        responses_df = self.utils_service.reorder_dataframe(responses_df)

        output_local_file, file_name = self.excel_service.save_file_to_excel(
            client_document, responses_df
        )

        self.excel_service.write_data_sheet(
            output_local_file, responses_df, sub_modules
        )
        self.excel_service.write_notes_sheet(output_local_file)

        output_s3_file = self.file_service.save_file_to_s3(
            client_document, file_name, output_local_file
        )

        return {
            "output_s3_file": output_s3_file,
            "output_local_file": output_local_file,
        }

    def _apply_environmental(self, input_df):
        land_service = LandService()
        input_df = land_service.get_car_metadata(input_df)
        input_df = land_service.get_owners_dataframe(input_df)
        input_df = land_service.process_rectified_car(input_df)
        return input_df

    def _apply_esg(self, input_df):
        esg_service = ESGService()
        input_df = esg_service.process_esg_intersections(input_df)
        return input_df

    def _apply_credit(self, input_df, **kwargs):
        credit_service = CreditService()
        input_df = credit_service.apply_agro_related(input_df)
        input_df = credit_service.apply_bndes(input_df)
        input_df = credit_service.apply_basic_info(input_df)
        input_df = credit_service.apply_sintegra(input_df)
        input_df = credit_service.apply_protests(input_df)
        input_df = credit_service.apply_federal_revenue(input_df)
        input_df = credit_service.apply_fgts(input_df)
        input_df = credit_service.apply_cndt(input_df)
        input_df = credit_service.invoke_lambda(input_df, kwargs.get("client_document"))
        input_df = credit_service.apply_business_rules(input_df)
        return input_df

    def _apply_soil_use(self, input_df):
        soil_use_service = SoilUseService()
        utils_service = UtilsService()

        cars = input_df["car"].tolist()
        formatted_cars = [utils_service.format_car(input_car=car) for car in cars]

        all_data = soil_use_service.get_soil_use(cars=formatted_cars)

        for index, row in input_df.iterrows():
            car = row["car"]
            if car in all_data:
                data = all_data[car]
                for column_name, value in data.items():
                    input_df.at[index, column_name] = value

        input_df = soil_use_service.check_landmarks(input_df)

        return input_df

    def get_agrowatch_payload(self, input_df):
        input_df["payload"] = input_df["documento"].apply(
            lambda doc: {
                "document": self.utils_service.clean_document(doc),
                "source": "federal",
            }
        )
        payloads = input_df["payload"].tolist()
        documents = input_df["documento"].tolist()

        return payloads, documents

    def search_car_in_agrowatch_api(self, input_df):
        expanded_rows = []
        payloads, documents = self.get_agrowatch_payload(input_df)

        responses = asyncio.get_event_loop().run_until_complete(
            self.agrowatch_service.fetch_all(
                payloads, "/datasets/experian-agri/property-relations/document"
            )
        )

        for document, response in zip(documents, responses):
            print(response)
            results = json.loads(response)["records"]

            if isinstance(results, list):
                if results:
                    for result in results:
                        expanded_rows.append(
                            {
                                "documento": document,
                                "car": result["federal_car_code"],
                            }
                        )
                else:
                    expanded_rows.append({"documento": document, "car": "-"})
            else:
                expanded_rows.append(
                    {"documento": document, "car": "Consulta indisponível"}
                )

        return pd.DataFrame(expanded_rows)

    def get_car_geometries(self, input_df):
        car_list = input_df["car"].unique().tolist()
        query = """
            SELECT DISTINCT ON (federal_car_code)
            federal_car_code, geom
            FROM mgi_car_property_limits_a
            WHERE federal_car_code = ANY(%(car_list)s)
            ORDER BY federal_car_code, id DESC
        """
        gdf_base = gpd.read_postgis(
            query,
            con=self.utils_service.get_engine(),
            geom_col="geom",
            params={"car_list": car_list},
        )

        input_df = input_df.merge(
            gdf_base[["federal_car_code", "geom"]],
            how="left",
            left_on="car",
            right_on="federal_car_code",
        )
        input_df = self.utils_service.remove_columns(input_df, ["federal_car_code"])
        return input_df
